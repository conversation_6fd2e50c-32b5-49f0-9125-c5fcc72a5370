# Sidebar State Management Testing Guide

## Quick Test Checklist

Use this checklist to verify the sidebar improvements are working correctly:

### ✅ Default Collapsed State Test

1. Open the application in a new browser tab
2. Navigate to the main dashboard page
3. **Expected**: All sidebar groups with children (like "Documents") should be collapsed
4. **Pass/Fail**: \_\_\_

### ✅ Smart Expansion Test

1. Navigate directly to a child route: `/[org]/documents/policies`
2. Refresh the page (F5)
3. **Expected**:
   - "Documents" parent group should be automatically expanded
   - "Policies" child item should be highlighted as active
   - Other parent groups should remain collapsed
4. **Pass/Fail**: \_\_\_

### ✅ Multiple Child Routes Test

1. Navigate to `/[org]/documents/policies`
2. Then navigate to `/[org]/documents/procedures`
3. Refresh the page
4. **Expected**:
   - "Documents" parent group should remain expanded
   - "Procedures" child item should be highlighted as active
   - "Policies" should no longer be highlighted
5. **Pass/Fail**: \_\_\_

### ✅ Parent Group Switching Test

1. Navigate to `/[org]/documents/policies` (Documents group expanded)
2. Navigate to a different parent's child route (if available)
3. **Expected**:
   - Previous parent group should collapse
   - New parent group should expand
   - Correct child should be highlighted
4. **Pass/Fail**: \_\_\_

### ✅ Manual Interaction Test

1. Navigate to a child route to auto-expand a parent
2. Manually click the chevron to collapse the parent group
3. Navigate away and back to the same child route
4. **Expected**:
   - Parent group should auto-expand again
   - Child should be highlighted
5. **Pass/Fail**: \_\_\_

### ✅ Browser Navigation Test

1. Navigate to a child route
2. Use browser back button to go to a different route
3. Use browser forward button to return
4. **Expected**:
   - Sidebar state should update correctly with navigation
   - Smart expansion should work with browser navigation
5. **Pass/Fail**: \_\_\_

### ✅ Hard Refresh Test

1. Navigate to a child route
2. Perform a hard refresh (Ctrl+F5 or Cmd+Shift+R)
3. **Expected**:
   - Parent group should auto-expand
   - Child should be highlighted
   - Other groups should be collapsed
4. **Pass/Fail**: \_\_\_

### ✅ New Tab Test

1. Right-click on a child route link and open in new tab
2. **Expected**:
   - New tab should load with correct sidebar state
   - Parent group should be expanded
   - Child should be highlighted
3. **Pass/Fail**: \_\_\_

## Test Routes

Use these routes for testing (replace `[org]` with actual organization ID):

### Documents Module (has children)

- Parent: `/[org]/documents`
- Children:
  - `/[org]/documents/policies`
  - `/[org]/documents/procedures`
  - `/[org]/documents/general`

### Other Modules (no children)

- `/[org]/dashboard`
- `/[org]/controls`
- `/[org]/evidences`
- `/[org]/trainings`
- `/[org]/questionnaires`

## Debugging Tips

If tests fail, check these common issues:

### Console Errors

1. Open browser developer tools (F12)
2. Check for JavaScript errors in the console
3. Look for React warnings or errors

### State Inspection

1. Install React Developer Tools browser extension
2. Navigate to Components tab
3. Find `SidebarProvider` component
4. Inspect `collapsibleItems` state

### Network Issues

1. Check Network tab for failed API requests
2. Verify organization and route data is loading correctly

### Route Detection

1. Add temporary console.log statements to debug route detection:
   ```typescript
   console.log("Current path:", currentPath);
   console.log("Is child route active:", isChildRouteActive(child.path));
   ```

## Performance Verification

### Check for Unnecessary Re-renders

1. Open React Developer Tools
2. Enable "Highlight updates when components render"
3. Navigate between routes
4. **Expected**: Sidebar should not flash/re-render excessively

### Memory Leaks

1. Navigate between multiple routes
2. Check browser memory usage doesn't continuously increase
3. Use browser's Memory tab in developer tools if needed

## Reporting Issues

If any tests fail, please report with:

1. Browser and version
2. Specific test that failed
3. Expected vs actual behavior
4. Console errors (if any)
5. Steps to reproduce

## Success Criteria

All tests should pass for the implementation to be considered successful:

- ✅ Default collapsed state on fresh page load
- ✅ Smart expansion when child routes are active
- ✅ Proper state management across navigation
- ✅ No performance regressions
- ✅ Backward compatibility maintained
