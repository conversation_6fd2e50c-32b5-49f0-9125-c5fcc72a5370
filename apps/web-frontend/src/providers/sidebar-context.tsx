"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { usePathname } from "next/navigation";

export type SidebarType = "dashboard" | "organization";

interface SidebarContextType {
  activeSidebar: SidebarType;
  setActiveSidebar: (type: SidebarType) => void;
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  // New state for managing collapsible sidebar items
  collapsibleItems: Record<string, boolean>;
  setCollapsibleItems: (items: Record<string, boolean>) => void;
  toggleCollapsibleItem: (itemId: string) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

// Determine which sidebar should be active based on current pathname
function deriveSidebar(path: string): SidebarType {
  return path.includes("/settings") ? "organization" : "dashboard";
}

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // 1. Initialise sidebar type based on current URL so refresh keeps correct sidebar
  const [activeSidebar, setActiveSidebarState] = useState<SidebarType>(() =>
    deriveSidebar(pathname),
  );

  // 2. Collapsed state lives only in memory for the session
  const [sidebarCollapsed, setSidebarCollapsedState] = useState<boolean>(false);

  // 3. Collapsible items state - defaults to all collapsed, but will be managed by individual sidebar components
  const [collapsibleItems, setCollapsibleItemsState] = useState<Record<string, boolean>>({});

  // 4. Update active sidebar whenever route changes (e.g., back/forward navigation)
  useEffect(() => {
    setActiveSidebarState(deriveSidebar(pathname));
  }, [pathname]);

  // (No persistence for collapsed preference – keep session-only)

  const setActiveSidebar = (type: SidebarType) => setActiveSidebarState(type);

  const setSidebarCollapsed = (collapsed: boolean) =>
    setSidebarCollapsedState(collapsed);

  const setCollapsibleItems = (items: Record<string, boolean>) =>
    setCollapsibleItemsState(items);

  const toggleCollapsibleItem = (itemId: string) => {
    setCollapsibleItemsState((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  return (
    <SidebarContext.Provider
      value={{
        activeSidebar,
        setActiveSidebar,
        sidebarCollapsed,
        setSidebarCollapsed,
        collapsibleItems,
        setCollapsibleItems,
        toggleCollapsibleItem,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
}
