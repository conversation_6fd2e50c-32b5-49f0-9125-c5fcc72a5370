"use client";

import React, { useState, useEffect } from "react";
import { X, ChevronDown, ChevronRight } from "lucide-react";
import { DashboardSidebarProps, CollapsibleState } from "@/types/dashboard";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useOrgRoutes } from "@/hooks/use-org-routes";
import { useOrganization } from "@/providers/organization-provider";
import { useSidebar } from "@/providers/sidebar-context";
import { buildOrgRoute } from "@/lib/routes";
import { Skeleton } from "@askinfosec/shadcn-ui/components/ui/skeleton";
import { useRBAC } from "@/providers/rbac-provider";

export const DashboardSidebar: React.FC<DashboardSidebarProps> = ({
  isCollapsed,
  onClose,
  isMobile = false,
  currentPath = "",
}) => {
  const { userRole } = useRBAC();
  // Use the simplified organization routes hook
  const {
    orgRoutes,
    orgSettingsRoute,
    hasOrganization,
    isRouteActive,
    isChildRouteActive,
    isLoading: modulesLoading,
  } = useOrgRoutes();

  // Get organization loading state and current organization ID
  const { isLoading: organizationLoading, currentOrganizationId } =
    useOrganization();

  // Get sidebar context for switching to organization sidebar
  const { setActiveSidebar } = useSidebar();

  // Get router for navigation
  const router = useRouter();

  // Use centralized collapsible state from sidebar context
  const { collapsibleItems, setCollapsibleItems, toggleCollapsibleItem } =
    useSidebar();
  const [selectedParentItem, setSelectedParentItem] = useState<string | null>(
    null,
  );

  // Initialize collapsible items and handle smart expansion based on current route
  useEffect(() => {
    const initialState: CollapsibleState = {};
    let hasActiveChild = false;
    let activeParentId: string | null = null;

    orgRoutes.forEach((item) => {
      if (item.children) {
        // Default all parent items to collapsed (true)
        initialState[item.id] = true;

        // Check if any child is currently active
        const childIsActive = item.children.some(
          (child) =>
            child.path &&
            (isChildRouteActive(child.path) ||
              currentPath.includes(child.path)),
        );

        if (childIsActive) {
          // If a child is active, expand this parent (false = expanded)
          initialState[item.id] = false;
          hasActiveChild = true;
          activeParentId = item.id;
        }
      }
    });

    // Only update if the state has changed to avoid unnecessary re-renders
    const currentState = collapsibleItems;
    const stateChanged =
      Object.keys(initialState).some(
        (key) => currentState[key] !== initialState[key],
      ) ||
      Object.keys(currentState).length !== Object.keys(initialState).length;

    if (stateChanged) {
      setCollapsibleItems(initialState);
    }

    // Clear any manual parent selection if we have an active child
    if (hasActiveChild && selectedParentItem) {
      setSelectedParentItem(null);
    }
  }, [orgRoutes, currentPath, isChildRouteActive, setCollapsibleItems]); // Removed collapsibleItems and selectedParentItem from deps to avoid infinite loops

  const toggleCollapse = (itemId: string) => {
    toggleCollapsibleItem(itemId);
    // Set this parent item as selected, clear any other parent selection
    setSelectedParentItem(itemId);
  };

  const isItemActive = (item: (typeof orgRoutes)[0]) => {
    if (item.children) {
      // Parent item is active if any of its children are active OR if it's manually selected
      const hasActiveChild = item.children.some(
        (child) =>
          (child.path && isChildRouteActive(child.path)) ||
          (child.path && currentPath.includes(child.path)),
      );

      // If a child is active, clear the manual selection
      if (hasActiveChild && selectedParentItem === item.id) {
        setSelectedParentItem(null);
      }

      return hasActiveChild || selectedParentItem === item.id;
    }

    // For regular items, clear any parent selection when they become active
    const isActive =
      isRouteActive(item.path) ||
      (item.path && currentPath.includes(item.path));
    if (isActive && selectedParentItem) {
      setSelectedParentItem(null);
    }

    return isActive;
  };

  const renderNavItem = (item: (typeof orgRoutes)[0]) => {
    const isActive = isItemActive(item);
    const isItemCollapsed = collapsibleItems[item.id];

    if (item.children) {
      // Render parent item with children
      return (
        <div key={item.id} className="space-y-1">
          <div className="relative">
            {/* Parent item container */}
            <div
              onClick={(e) => {
                // Only navigate if the click wasn't on the chevron
                const target = e.target as unknown as {
                  closest?: (selector: string) => unknown;
                };
                if (
                  !target ||
                  !target.closest ||
                  !target.closest("[data-chevron]")
                ) {
                  if (!isCollapsed && item.path) {
                    // Navigate to parent path if it exists
                    window.location.href = item.path;
                  }
                }
              }}
              className={cn(
                "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group h-10",
                isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
                isActive
                  ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
                // Disable pointer events if no path (for backward compatibility)
                !item.path && "cursor-default",
              )}
            >
              <item.icon
                className={cn(
                  "transition-colors duration-200",
                  isCollapsed ? "h-7 w-7" : "h-6 w-6",
                  isActive
                    ? "text-primary"
                    : "text-muted-foreground group-hover:text-accent-foreground",
                )}
              />

              {!isCollapsed && (
                <>
                  <div className="ml-3 min-w-0 flex-1">
                    <div className="font-medium truncate">{item.name}</div>
                  </div>

                  {/* Chevron button inside the main container */}
                  <button
                    data-chevron
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent parent click
                      toggleCollapse(item.id);
                    }}
                    className="ml-2 p-1.5 rounded-md hover:bg-accent/50 transition-colors duration-200"
                  >
                    {isItemCollapsed ? (
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-muted-foreground" />
                    )}
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Render children */}
          {!isCollapsed && !isItemCollapsed && (
            <div className="ml-4 space-y-1">
              {item.children.map((child) => {
                if (!child.path) return null;

                return (
                  <Link
                    key={child.id}
                    href={child.path as string}
                    className={cn(
                      "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group px-3 py-2 h-9",
                      isChildRouteActive(child.path)
                        ? "bg-accent/60 text-accent-foreground border border-accent/50 shadow-sm"
                        : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground border border-transparent",
                    )}
                  >
                    <child.icon
                      className={cn(
                        "h-4 w-4 transition-colors duration-200",
                        isChildRouteActive(child.path)
                          ? "text-primary"
                          : "text-muted-foreground group-hover:text-accent-foreground",
                      )}
                    />
                    <div className="ml-3 min-w-0 flex-1">
                      <div className="font-medium truncate">{child.name}</div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>
      );
    }

    // Render regular item without children
    if (!item.path) return null;

    return (
      <Link
        key={item.id}
        href={item.path as string}
        className={cn(
          "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group h-10",
          isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
          isActive
            ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
        )}
      >
        <item.icon
          className={cn(
            "transition-colors duration-200",
            isCollapsed ? "h-7 w-7" : "h-6 w-6",
            isActive
              ? "text-primary"
              : "text-muted-foreground group-hover:text-accent-foreground",
          )}
        />

        {!isCollapsed && (
          <div className="ml-3 min-w-0 flex-1">
            <div className="font-medium truncate">{item.name}</div>
          </div>
        )}
      </Link>
    );
  };

  // Show loading state if organization is loading, modules are loading, or no organization is selected
  if (organizationLoading || modulesLoading || !hasOrganization) {
    // console.debug("[DashboardSidebar] Loading state check:", {
    //   organizationLoading,
    //   modulesLoading,
    //   hasOrganization,
    //   currentOrganizationId,
    //   orgRoutesLength: orgRoutes.length,
    //   orgSettingsRoute: !!orgSettingsRoute,
    // });

    return (
      <div className="flex flex-col h-full bg-card no-border shadow-sm">
        {/* Logo/Brand - Fixed height to match header */}
        <div className="flex items-center justify-between h-12 flex-shrink-0 bg-background">
          <div className="flex items-center space-x-3 px-4">
            <Skeleton className="w-9 h-9 rounded-lg" />
            <Skeleton className="h-6 w-24" />
          </div>
        </div>

        {/* Navigation - Loading state */}
        <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto">
          <div className="mb-4">
            <Skeleton className="h-4 w-16" />
          </div>
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-3 px-2 py-2">
              <Skeleton className="w-5 h-5 rounded" />
              <Skeleton className="h-4 flex-1" />
            </div>
          ))}
        </nav>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-card no-border shadow-sm transition-all duration-300",
        isCollapsed ? "w-14" : "w-64",
      )}
    >
      {/* Logo/Brand - Fixed height to match header */}
      <div className="flex items-center justify-between h-12 no-border flex-shrink-0 bg-background">
        {isCollapsed ? (
          <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm mx-auto shadow-sm">
            A
          </div>
        ) : (
          <div className="flex items-center space-x-3 px-4">
            <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm shadow-sm">
              A
            </div>
            <span className="font-bold text-foreground text-lg">
              AskInfosec
            </span>
          </div>
        )}
        {isMobile && onClose && (
          <button
            type="button"
            className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors duration-200"
            onClick={onClose}
          >
            <span className="sr-only">Close sidebar</span>
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Navigation - Scrollable area */}
      <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto">
        {/* <div className={cn(isCollapsed ? "hidden" : "block", "mb-4")}>
          <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Modules
          </h3>
        </div> */}

        {orgRoutes.map((item) => renderNavItem(item))}
      </nav>

      {/* Organization Settings - Fixed at bottom */}
      {(userRole === "owner" || userRole === "admin") && orgSettingsRoute && (
        <div className="px-3 h-12 no-border flex-shrink-0 flex items-center">
          <button
            onClick={() => {
              setActiveSidebar("organization");
              // Navigate to General settings page
              if (currentOrganizationId) {
                router.push(
                  buildOrgRoute(currentOrganizationId, "/settings/general"),
                );
              }
            }}
            className={cn(
              "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer h-10",
              isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
              isRouteActive(orgSettingsRoute.path)
                ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
            )}
          >
            <orgSettingsRoute.icon
              className={cn(
                "transition-colors duration-200",
                isCollapsed ? "h-7 w-7" : "h-6 w-6",
                isRouteActive(orgSettingsRoute.path)
                  ? "text-primary"
                  : "text-muted-foreground",
              )}
            />
            {!isCollapsed && (
              <span className="ml-3">{orgSettingsRoute.name}</span>
            )}
          </button>
        </div>
      )}
    </div>
  );
};
