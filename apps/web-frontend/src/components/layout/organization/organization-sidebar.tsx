"use client";

import React from "react";
import {
  X,
  ArrowLeft,
  <PERSON>tings,
  CreditCard,
  Users,
  Package2,
  WandSparkles,
} from "lucide-react";
import { OrganizationSidebarProps } from "@/types/organization";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useOrganization } from "@/providers/organization-provider";
import { buildOrgRoute } from "@/lib/routes";

const ORGANIZATION_NAV_ITEMS = [
  {
    id: "general",
    name: "General",
    description: "General organization settings",
    path: "/settings/general",
    icon: Settings,
  },
  {
    id: "members",
    name: "Members",
    description: "Team member management",
    path: "/settings/members",
    icon: Users,
  },
  {
    id: "billing",
    name: "Billing & Subscription",
    description: "Manage billing and subscription",
    path: "/settings/billing",
    icon: CreditCard,
  },
  {
    id: "products",
    name: "Products",
    description: "Manage products",
    path: "/settings/products",
    icon: Package2,
  },
  {
    id: "ai",
    name: "AI",
    description: "AI",
    path: "/settings/ai",
    icon: WandSpark<PERSON>,
  },
];

export const OrganizationSidebar: React.FC<OrganizationSidebarProps> = ({
  isCollapsed,
  onClose,
  isMobile = false,
  currentPath: _currentPath = "",
  onBackToDashboard,
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const { currentOrganizationId } = useOrganization();

  // Build organization-aware navigation items
  const orgNavItems = ORGANIZATION_NAV_ITEMS.map((item) => ({
    ...item,
    path: currentOrganizationId
      ? buildOrgRoute(currentOrganizationId, item.path)
      : item.path,
  }));

  const isRouteActive = (routePath: string): boolean => {
    return pathname.includes(routePath);
  };

  const handleBackToDashboard = () => {
    // Switch the sidebar back to dashboard and navigate immediately
    onBackToDashboard();
    if (currentOrganizationId) {
      router.push(buildOrgRoute(currentOrganizationId, "/dashboard"));
    }
  };

  const renderNavItem = (item: (typeof orgNavItems)[0]) => {
    const isActive = isRouteActive(item.path);

    return (
      <Link
        key={item.id}
        href={item.path}
        className={cn(
          "flex items-center w-full text-left text-sm rounded-lg transition-all duration-200 cursor-pointer group h-10",
          isCollapsed ? "justify-center px-1 py-2" : "px-3 py-2",
          isActive
            ? "bg-gradient-to-r from-accent to-accent/80 text-accent-foreground border border-accent shadow-sm"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-transparent",
        )}
      >
        <item.icon
          className={cn(
            "transition-colors duration-200",
            isCollapsed ? "h-7 w-7" : "h-6 w-6",
            isActive
              ? "text-primary"
              : "text-muted-foreground group-hover:text-accent-foreground",
          )}
        />

        {!isCollapsed && (
          <div className="ml-3 min-w-0 flex-1">
            <div className="font-medium truncate">{item.name}</div>
          </div>
        )}
      </Link>
    );
  };

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-card no-border shadow-sm transition-all duration-300",
        isCollapsed ? "w-14" : "w-64",
      )}
    >
      {/* Logo/Brand with Back Button */}
      <div className="flex items-center justify-between h-12 no-border flex-shrink-0 bg-background">
        {isCollapsed ? (
          <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm mx-auto shadow-sm">
            A
          </div>
        ) : (
          <div className="flex items-center justify-between w-full px-4">
            <div className="flex items-center space-x-3">
              <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center text-primary-foreground font-bold text-sm shadow-sm">
                A
              </div>
              <span className="font-bold text-foreground text-lg">
                AskInfosec
              </span>
            </div>

            {/* Back Button */}
            <button
              onClick={handleBackToDashboard}
              className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors duration-200"
              title="Back to Dashboard"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          </div>
        )}

        {isMobile && onClose && (
          <button
            type="button"
            className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors duration-200"
            onClick={onClose}
          >
            <span className="sr-only">Close sidebar</span>
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Navigation - Scrollable area */}
      <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto">
        {/* <div className={cn(isCollapsed ? "hidden" : "block", "mb-4")}>
          <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Organization Settings
          </h3>
        </div> */}

        {orgNavItems.map((item) => renderNavItem(item))}
      </nav>
    </div>
  );
};
