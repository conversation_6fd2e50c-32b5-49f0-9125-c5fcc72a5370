"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, User, AlertCircle, Refresh<PERSON>w } from "lucide-react";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";
import type { ChatMessage } from "@/hooks/use-ai-assistant";

interface ChatMessageProps {
  message: ChatMessage;
  onRetry?: () => void;
}

export function ChatMessageComponent({ message, onRetry }: ChatMessageProps) {
  const isUser = message.role === "user";
  // const isAssistant = message.role === "assistant";
  const hasError = !!message.error;
  const isStreaming = message.isStreaming;

  return (
    <div
      className={cn(
        "flex gap-3 p-4 group",
        isUser && "flex-row-reverse",
        hasError && "bg-destructive/5",
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
          isUser
            ? "bg-primary text-primary-foreground"
            : "bg-muted text-muted-foreground",
        )}
      >
        {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
      </div>

      {/* Message Content */}
      <div className={cn("flex-1 min-w-0", isUser && "text-right")}>
        {/* Message Text */}
        <div
          className={cn(
            "inline-block max-w-[85%] p-3 rounded-lg text-sm",
            isUser
              ? "bg-primary text-primary-foreground ml-auto"
              : "bg-muted text-foreground",
            hasError && "border border-destructive",
          )}
        >
          {message.content || (isStreaming ? "..." : "No response")}

          {/* Streaming indicator */}
          {isStreaming && !hasError && (
            <span className="inline-block w-2 h-4 ml-1 bg-current opacity-50 animate-pulse" />
          )}
        </div>

        {/* Error Message */}
        {hasError && (
          <div className="mt-2 flex items-center gap-2 text-sm text-destructive">
            <AlertCircle className="w-4 h-4" />
            <span>{message.error}</span>
            {onRetry && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRetry}
                className="h-6 px-2 text-xs"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Retry
              </Button>
            )}
          </div>
        )}

        {/* Sources */}
        {message.sources && message.sources.length > 0 && (
          <div className="mt-2 text-xs text-muted-foreground">
            <div className="font-medium mb-1">Sources:</div>
            <div className="space-y-1">
              {message.sources.map((source, index) => (
                <div
                  key={source.id || index}
                  className="flex items-center gap-2"
                >
                  <span className="w-1 h-1 bg-current rounded-full" />
                  <span>{source.title}</span>
                  {source.confidence && (
                    <span className="text-xs opacity-70">
                      ({Math.round(source.confidence * 100)}%)
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Metadata */}
        {message.metadata && (
          <div className="mt-1 text-xs text-muted-foreground opacity-70">
            {message.metadata.model && (
              <span>Model: {message.metadata.model}</span>
            )}
            {message.metadata.tokensUsed && (
              <span className="ml-2">
                Tokens: {message.metadata.tokensUsed}
              </span>
            )}
          </div>
        )}

        {/* Timestamp */}
        <div className="mt-1 text-xs text-muted-foreground opacity-50">
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
}
