import { Injectable } from '@nestjs/common';
import {
  OrganizationModuleRepository,
  OrganizationRepository,
  type OrganizationModuleAccess,
  type SessionContext,
} from '@askinfosec/database-drizzle';
import { SubscriptionsService } from '../../subscriptions/services/subscriptions.service';
import Strip<PERSON> from 'stripe';

@Injectable()
export class OrganizationModulesService {
  constructor(
    private readonly organizationRepository: OrganizationRepository,
    private readonly organizationModuleRepository: OrganizationModuleRepository,
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  /**
   * Get complete module access for a specific organization.
   *
   * This method calculates module access based on:
   * 1. Active subscriptions and their associated products
   * 2. Organization module overrides (enabled/disabled modules)
   * 3. Automatic dependency resolution
   * 4. Deduplication by module ID to prevent duplicates
   *
   * @param organizationId - The ID of the organization
   * @param context - Optional session context for RLS
   * @returns Array of unique modules the organization has access to with source information
   *
   * @note This method is the primary way for the frontend to determine
   * what modules and features should be available to users in the organization.
   * The final module access is stored in organization.modules field but this
   * method provides the calculated view including dependencies and deduplication.
   *
   * @example
   * ```typescript
   * const modules = await service.getOrganizationModuleAccess('org_123');
   * // Returns deduplicated array of modules from subscriptions and overrides
   * ```
   */
  async getOrganizationModuleAccess(
    organizationId: string,
    context?: SessionContext,
  ): Promise<OrganizationModuleAccess[]> {
    let orgModules: OrganizationModuleAccess[] = [];
    try {
      const organization = await this.organizationRepository.findById(
        organizationId,
        context,
      );
      console.log(
        '[getOrganizationModuleAccess] Organization find by id',
        organization,
      );
      const promiseArray = [
        // Get modules that might directly added to the organization through overrides, core or directly to organization.modules
        this.organizationModuleRepository.getOrganizationModuleAccess(
          organizationId,
          context,
        ) as unknown as Promise<OrganizationModuleAccess[]>,
        // Get active subscriptions directly from stripe
        this.subscriptionsService.getAllSubscriptions(
          organization?.stripeCustomerId ?? '',
        ) as Promise<Stripe.Subscription[]>,
      ] as [
        Promise<OrganizationModuleAccess[]>,
        Promise<Stripe.Subscription[]>,
      ];
      // Results from the promises. `modules` will merge with modules that we will get from the subscriptions. Subscription is linked to a product, and product is linked to a module.
      const [modules, subscriptions] = await Promise.all(promiseArray);
      // Get modules that are linked to the products of the subscriptions
      for (const subscription of subscriptions) {
        const mods =
          await this.organizationModuleRepository.getModulesByInternalProductId(
            subscription.items.data[0].price.product as string,
          );
        orgModules = [...orgModules, ...mods];
      }
      // Combine and deduplicate modules by ID
      const allModules = [...modules, ...orgModules];
      const uniqueModules = allModules.reduce((acc, module) => {
        if (!acc.find((m) => m.id === module.id)) {
          acc.push(module);
        }
        return acc;
      }, [] as OrganizationModuleAccess[]);
      orgModules = uniqueModules;
    } catch (error) {
      console.error(error);
    }
    return orgModules;
  }
}
