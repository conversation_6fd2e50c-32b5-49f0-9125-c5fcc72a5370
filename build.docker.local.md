# Local Docker Build and Run

## Database

```bash
export DOCKER_NETWORK="development"
docker network create $DOCKER_NETWORK
docker run -d \
	--name postgres \
    -p 5432:5432 \
	-e POSTGRES_USER=postgres \
	-e POSTGRES_PASSWORD=password \
	-e PGDATA=/var/lib/postgresql/data \
    --network $DOCKER_NETWORK \
	pgvector/pgvector:0.8.1-pg17-trixie
```

## API

```bash
# Build the image
docker build -t askinfosec-api -f apps/api/Dockerfile .

# Run the container interactively
docker run -it --rm --name askinfosec-api -p 3000:3000 --network development --env-file apps/api/.env askinfosec-api

# Run the container
docker run --rm --rm --name askinfosec-api -p 3000:3000 --network development --env-file apps/api/.env askinfosec-api
```
